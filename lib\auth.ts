import { createClient as createServer<PERSON>lient } from '@/lib/supabase/server'
import { createClient as createBrowserClient } from '@/lib/supabase/client'
import { User, Session } from '@supabase/supabase-js'
import { redirect } from 'next/navigation'

export type AuthUser = {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export type AuthSession = {
  user: AuthUser
  session: Session
}

/**
 * Get the current authenticated user from server session
 */
export async function getCurrentUser(): Promise<AuthUser | null> {
  try {
    const supabase = createServerClient()
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return null
    }

    // Fetch user profile from database
    const { data: profile } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()

    if (!profile) {
      return null
    }

    return {
      id: user.id,
      email: user.email!,
      full_name: profile.full_name || undefined,
      avatar_url: profile.avatar_url || undefined,
      created_at: profile.created_at,
      updated_at: profile.updated_at,
    }
  } catch (error) {
    console.error('Error getting current user:', error)
    return null
  }
}

/**
 * Get the current session from server (DEPRECATED - use getCurrentUser instead)
 * Note: This should only be used for client-side components where getSession() is safe
 */
export async function getSession(): Promise<Session | null> {
  try {
    const supabase = createServerClient()
    // For server-side use, we should use getUser() instead for security
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error || !user) {
      return null
    }

    // Create a session-like object for compatibility
    return {
      user,
      access_token: '', // Not available via getUser()
      refresh_token: '', // Not available via getUser()
      expires_in: 0,
      expires_at: 0,
      token_type: 'bearer'
    } as Session
  } catch (error) {
    console.error('Error getting session:', error)
    return null
  }
}

/**
 * Refresh the current session
 */
export async function refreshSession(): Promise<Session | null> {
  try {
    const supabase = createServerClient()
    const { data: { session }, error } = await supabase.auth.refreshSession()
    
    if (error || !session) {
      return null
    }

    return session
  } catch (error) {
    console.error('Error refreshing session:', error)
    return null
  }
}

/**
 * Require authentication for a route - redirects if not authenticated
 */
export async function requireAuth(): Promise<AuthUser> {
  const user = await getCurrentUser()
  
  if (!user) {
    redirect('/login')
  }
  
  return user
}

/**
 * Create user profile in database after authentication
 */
export async function createUserProfile(user: User): Promise<AuthUser | null> {
  try {
    const supabase = createServerClient()
    
    const userData = {
      id: user.id,
      email: user.email!,
      full_name: user.user_metadata?.full_name || user.user_metadata?.name || null,
      avatar_url: user.user_metadata?.avatar_url || user.user_metadata?.picture || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    const { data, error } = await supabase
      .from('users')
      .upsert(userData, { onConflict: 'id' })
      .select()
      .single()

    if (error) {
      console.error('Error creating user profile:', error)
      return null
    }

    return {
      id: data.id,
      email: data.email,
      full_name: data.full_name || undefined,
      avatar_url: data.avatar_url || undefined,
      created_at: data.created_at,
      updated_at: data.updated_at,
    }
  } catch (error) {
    console.error('Error creating user profile:', error)
    return null
  }
}

/**
 * Get user profile by ID
 */
export async function getUserProfile(userId: string): Promise<AuthUser | null> {
  try {
    const supabase = createServerClient()
    
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()

    if (error || !data) {
      return null
    }

    return {
      id: data.id,
      email: data.email,
      full_name: data.full_name || undefined,
      avatar_url: data.avatar_url || undefined,
      created_at: data.created_at,
      updated_at: data.updated_at,
    }
  } catch (error) {
    console.error('Error getting user profile:', error)
    return null
  }
}

/**
 * Check if user is authenticated (client-side)
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const supabase = createBrowserClient()
    const { data: { session } } = await supabase.auth.getSession()
    return !!session
  } catch (error) {
    return false
  }
}

/**
 * Sign out user and clear session
 */
export async function signOut(): Promise<void> {
  try {
    const supabase = createBrowserClient()
    await supabase.auth.signOut()
  } catch (error) {
    console.error('Error signing out:', error)
  }
}

/**
 * Get user from client session (for client components)
 */
export async function getCurrentUserClient(): Promise<User | null> {
  try {
    const supabase = createBrowserClient()
    const { data: { user } } = await supabase.auth.getUser()
    return user
  } catch (error) {
    console.error('Error getting current user client:', error)
    return null
  }
} 