import { z } from 'zod';

// Permission validation schema
export const permissionSchema = z.object({
  resource: z.string().min(1, 'Resource is required'),
  action: z.string().min(1, 'Action is required'),
  scope: z.string().optional(),
});

// Role schemas
export const createRoleSchema = z.object({
  name: z
    .string()
    .min(1, 'Role name is required')
    .max(50, 'Role name must be 50 characters or less')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Role name can only contain letters, numbers, spaces, hyphens, and underscores'),
  description: z
    .string()
    .max(500, 'Description must be 500 characters or less')
    .optional(),
  permissions: z
    .array(permissionSchema)
    .min(1, 'At least one permission is required'),
});

export const updateRoleSchema = createRoleSchema.partial();

// User role assignment schemas
export const assignRoleSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  roleId: z.string().uuid('Invalid role ID'),
  expiresAt: z.string().datetime().optional(),
});

export const removeRoleSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  roleId: z.string().uuid('Invalid role ID'),
});

export const bulkAssignRolesSchema = z.object({
  userIds: z.array(z.string().uuid()).min(1, 'At least one user ID is required'),
  roleId: z.string().uuid('Invalid role ID'),
  expiresAt: z.string().datetime().optional(),
});

// Team invitation schemas
export const inviteTeamMemberSchema = z.object({
  email: z.string().email('Invalid email address'),
  roleId: z.string().uuid('Invalid role ID').optional(),
  message: z.string().max(1000, 'Message must be 1000 characters or less').optional(),
});

export const acceptInvitationSchema = z.object({
  token: z.string().min(1, 'Invitation token is required'),
  fullName: z.string().min(1, 'Full name is required').optional(),
});

export const cancelInvitationSchema = z.object({
  invitationId: z.string().uuid('Invalid invitation ID'),
});

// Permission checking schemas
export const checkPermissionSchema = z.object({
  permission: permissionSchema,
  userId: z.string().uuid().optional(),
});

export const checkPermissionsSchema = z.object({
  permissions: z.array(permissionSchema).min(1),
  userId: z.string().uuid().optional(),
  requireAll: z.boolean().default(false),
});

// Query schemas
export const roleQuerySchema = z.object({
  organizationId: z.string().uuid('Invalid organization ID'),
  includeSystemRoles: z.boolean().default(true),
  includePermissions: z.boolean().default(true),
});

export const userRoleQuerySchema = z.object({
  organizationId: z.string().uuid('Invalid organization ID'),
  userId: z.string().uuid().optional(),
  includeExpired: z.boolean().default(false),
});

export const auditLogQuerySchema = z.object({
  organizationId: z.string().uuid('Invalid organization ID'),
  userId: z.string().uuid().optional(),
  action: z.enum(['assigned', 'removed', 'modified', 'created', 'deleted']).optional(),
  limit: z.number().int().min(1).max(100).default(50),
  offset: z.number().int().min(0).default(0),
});

export const teamMembersQuerySchema = z.object({
  organizationId: z.string().uuid('Invalid organization ID'),
  includeInactive: z.boolean().default(false),
  includeRoles: z.boolean().default(true),
});

// Team member management schemas
export const updateTeamMemberSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
  role: z.enum(['owner', 'admin', 'manager', 'staff']).optional(),
  isActive: z.boolean().optional(),
});

export const removeTeamMemberSchema = z.object({
  userId: z.string().uuid('Invalid user ID'),
});

// Validation helper functions
export const validatePermissions = (permissions: unknown) => {
  return z.array(permissionSchema).safeParse(permissions);
};

export const validateRole = (role: unknown) => {
  return createRoleSchema.safeParse(role);
};

export const validateUserRole = (userRole: unknown) => {
  return assignRoleSchema.safeParse(userRole);
};

export const validateInvitation = (invitation: unknown) => {
  return inviteTeamMemberSchema.safeParse(invitation);
};

// Type exports for the schemas
export type PermissionInput = z.infer<typeof permissionSchema>;
export type CreateRoleInput = z.infer<typeof createRoleSchema>;
export type UpdateRoleInput = z.infer<typeof updateRoleSchema>;
export type AssignRoleInput = z.infer<typeof assignRoleSchema>;
export type RemoveRoleInput = z.infer<typeof removeRoleSchema>;
export type BulkAssignRolesInput = z.infer<typeof bulkAssignRolesSchema>;
export type InviteTeamMemberInput = z.infer<typeof inviteTeamMemberSchema>;
export type AcceptInvitationInput = z.infer<typeof acceptInvitationSchema>;
export type CheckPermissionInput = z.infer<typeof checkPermissionSchema>;
export type CheckPermissionsInput = z.infer<typeof checkPermissionsSchema>;
export type RoleQueryInput = z.infer<typeof roleQuerySchema>;
export type UserRoleQueryInput = z.infer<typeof userRoleQuerySchema>;
export type AuditLogQueryInput = z.infer<typeof auditLogQuerySchema>;
export type TeamMembersQueryInput = z.infer<typeof teamMembersQuerySchema>;
export type UpdateTeamMemberInput = z.infer<typeof updateTeamMemberSchema>;
export type RemoveTeamMemberInput = z.infer<typeof removeTeamMemberSchema>; 