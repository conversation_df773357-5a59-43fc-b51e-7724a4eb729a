import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/auth';
import { getTeamMembers, getTeamInvitations, createTeamInvitation } from '@/lib/db/rbac-queries';
import { inviteTeamMemberSchema } from '@/lib/validations/rbac';
import { requirePermission } from '@/lib/auth/rbac';
import { PERMISSIONS } from '@/types/roles';


export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId');
    const type = searchParams.get('type'); // 'members' or 'invitations'
    
    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    // Check permission to read team data
    await requirePermission(PERMISSIONS.USER_READ, organizationId);

    if (type === 'invitations') {
      const invitations = await getTeamInvitations(organizationId);
      return NextResponse.json({ invitations });
    } else {
      const members = await getTeamMembers(organizationId);
      return NextResponse.json({ members });
    }
  } catch (error: any) {
    console.error('Error fetching team data:', error);
    
    if (error.name === 'PermissionError') {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { organizationId, ...invitationData } = body;

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 });
    }

    // Check permission to invite users
    await requirePermission(PERMISSIONS.USER_INVITE, organizationId);

    // Validate invitation data
    const result = inviteTeamMemberSchema.safeParse(invitationData);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid invitation data', details: result.error.errors },
        { status: 400 }
      );
    }

    const invitation = await createTeamInvitation(organizationId, result.data, user.id);

    // TODO: Send invitation email here
    // await sendInvitationEmail(invitation);

    return NextResponse.json({ invitation }, { status: 201 });
  } catch (error: any) {
    console.error('Error creating invitation:', error);
    
    if (error.name === 'PermissionError') {
      return NextResponse.json({ error: error.message }, { status: 403 });
    }
    
    if (error.code === 'DUPLICATE_INVITATION') {
      return NextResponse.json({ error: error.message }, { status: 409 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 